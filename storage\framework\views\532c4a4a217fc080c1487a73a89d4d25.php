<?php $__env->startSection('title', 'Detail Pengiriman - Dashboard Supplier'); ?>
<?php $__env->startSection('page-title', 'Detail Pengiriman'); ?>

<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- Header -->
    <div class="supplier-dashboard-card">
        <div class="supplier-dashboard-card-content">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Detail Pengiriman</h1>
                    <p class="text-gray-600 mt-1">Informasi lengkap pengiriman produk ke gudang</p>
                </div>
                <div class="flex flex-col sm:flex-row gap-3">
                    <?php if($delivery->status === 'pending'): ?>
                    <a href="<?php echo e(route('supplier.deliveries.edit', $delivery)); ?>" class="supplier-dashboard-btn supplier-dashboard-btn-primary">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                        Edit Pengiriman
                    </a>
                    <?php endif; ?>
                    <a href="<?php echo e(route('supplier.deliveries.index')); ?>" class="supplier-dashboard-btn supplier-dashboard-btn-secondary">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Kembali ke Daftar
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Delivery Status Card -->
    <div class="supplier-dashboard-card">
        <div class="supplier-dashboard-card-content">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div class="flex items-center space-x-4">
                    <div class="supplier-dashboard-status-icon supplier-dashboard-status-<?php echo e($delivery->status); ?>">
                        <?php if($delivery->status === 'pending'): ?>
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        <?php elseif($delivery->status === 'received'): ?>
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        <?php elseif($delivery->status === 'partial'): ?>
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        <?php elseif($delivery->status === 'cancelled'): ?>
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        <?php endif; ?>
                    </div>
                    <div>
                        <h2 class="text-lg font-semibold text-gray-900">
                            Status: 
                            <span class="supplier-dashboard-status-badge supplier-dashboard-status-<?php echo e($delivery->status); ?>">
                                <?php if($delivery->status === 'pending'): ?> Menunggu Penerimaan
                                <?php elseif($delivery->status === 'received'): ?> Diterima Lengkap
                                <?php elseif($delivery->status === 'partial'): ?> Diterima Sebagian
                                <?php elseif($delivery->status === 'cancelled'): ?> Dibatalkan
                                <?php else: ?> <?php echo e(ucfirst($delivery->status)); ?>

                                <?php endif; ?>
                            </span>
                        </h2>
                        <p class="text-sm text-gray-600 mt-1">ID Pengiriman: <?php echo e($delivery->id); ?></p>
                    </div>
                </div>
                
                <?php if($delivery->status !== 'pending' && $delivery->status !== 'cancelled'): ?>
                <div class="supplier-dashboard-progress-container">
                    <div class="text-sm text-gray-600 mb-1">Progress Penerimaan</div>
                    <div class="supplier-dashboard-progress-bar">
                        <div class="supplier-dashboard-progress-fill" style="width: <?php echo e($delivery->completion_percentage); ?>%"></div>
                    </div>
                    <div class="text-xs text-gray-500 mt-1"><?php echo e($delivery->completion_percentage); ?>% selesai</div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Delivery Information -->
        <div class="supplier-dashboard-card">
            <div class="supplier-dashboard-card-header">
                <h3 class="supplier-dashboard-card-title">Informasi Pengiriman</h3>
            </div>
            <div class="supplier-dashboard-card-content">
                <div class="space-y-4">
                    <!-- Delivery Date -->
                    <div class="supplier-dashboard-info-row">
                        <div class="supplier-dashboard-info-label">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                            Tanggal Pengiriman
                        </div>
                        <div class="supplier-dashboard-info-value"><?php echo e(auth()->user()->formatDate($delivery->delivery_date)); ?></div>
                    </div>

                    <!-- Received Date -->
                    <?php if($delivery->received_date): ?>
                    <div class="supplier-dashboard-info-row">
                        <div class="supplier-dashboard-info-label">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Tanggal Diterima
                        </div>
                        <div class="supplier-dashboard-info-value"><?php echo e(auth()->user()->formatDate($delivery->received_date)); ?></div>
                    </div>
                    <?php endif; ?>

                    <!-- Supplier -->
                    <div class="supplier-dashboard-info-row">
                        <div class="supplier-dashboard-info-label">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                            Supplier
                        </div>
                        <div class="supplier-dashboard-info-value">
                            <div class="font-medium"><?php echo e($delivery->supplier->name); ?></div>
                            <?php if($delivery->supplier->contact_person): ?>
                            <div class="text-sm text-gray-500"><?php echo e($delivery->supplier->contact_person); ?></div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Received By -->
                    <?php if($delivery->receivedBy): ?>
                    <div class="supplier-dashboard-info-row">
                        <div class="supplier-dashboard-info-label">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                            Diterima Oleh
                        </div>
                        <div class="supplier-dashboard-info-value">
                            <div class="font-medium"><?php echo e($delivery->receivedBy->name); ?></div>
                            <div class="text-sm text-gray-500">Admin Gudang</div>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Product Information -->
        <div class="supplier-dashboard-card">
            <div class="supplier-dashboard-card-header">
                <h3 class="supplier-dashboard-card-title">Informasi Produk</h3>
            </div>
            <div class="supplier-dashboard-card-content">
                <div class="space-y-4">
                    <!-- Product Name -->
                    <div class="supplier-dashboard-info-row">
                        <div class="supplier-dashboard-info-label">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                            </svg>
                            Nama Produk
                        </div>
                        <div class="supplier-dashboard-info-value font-medium"><?php echo e($delivery->product->name); ?></div>
                    </div>

                    <!-- Quantity -->
                    <div class="supplier-dashboard-info-row">
                        <div class="supplier-dashboard-info-label">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14"></path>
                            </svg>
                            Jumlah Dikirim
                        </div>
                        <div class="supplier-dashboard-info-value font-medium"><?php echo e(number_format($delivery->quantity)); ?> unit</div>
                    </div>

                    <!-- Received Quantity -->
                    <?php if($delivery->received_quantity): ?>
                    <div class="supplier-dashboard-info-row">
                        <div class="supplier-dashboard-info-label">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Jumlah Diterima
                        </div>
                        <div class="supplier-dashboard-info-value font-medium text-green-600"><?php echo e(number_format($delivery->received_quantity)); ?> unit</div>
                    </div>
                    <?php endif; ?>

                    <!-- Remaining Quantity -->
                    <?php if($delivery->remaining_quantity > 0): ?>
                    <div class="supplier-dashboard-info-row">
                        <div class="supplier-dashboard-info-label">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            Sisa Belum Diterima
                        </div>
                        <div class="supplier-dashboard-info-value font-medium text-orange-600"><?php echo e(number_format($delivery->remaining_quantity)); ?> unit</div>
                    </div>
                    <?php endif; ?>

                    <!-- Unit Price -->
                    <?php if($delivery->unit_price): ?>
                    <div class="supplier-dashboard-info-row">
                        <div class="supplier-dashboard-info-label">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                            </svg>
                            Harga Satuan
                        </div>
                        <div class="supplier-dashboard-info-value">Rp <?php echo e(number_format($delivery->unit_price, 2, ',', '.')); ?></div>
                    </div>
                    <?php endif; ?>

                    <!-- Total Price -->
                    <?php if($delivery->total_price): ?>
                    <div class="supplier-dashboard-info-row">
                        <div class="supplier-dashboard-info-label">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                            Total Harga
                        </div>
                        <div class="supplier-dashboard-info-value font-semibold text-blue-600">Rp <?php echo e(number_format($delivery->total_price, 2, ',', '.')); ?></div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Notes Section -->
    <?php if($delivery->notes): ?>
    <div class="supplier-dashboard-card">
        <div class="supplier-dashboard-card-header">
            <h3 class="supplier-dashboard-card-title">Catatan Pengiriman</h3>
        </div>
        <div class="supplier-dashboard-card-content">
            <div class="supplier-dashboard-notes-content">
                <svg class="w-5 h-5 text-gray-400 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"></path>
                </svg>
                <p class="text-gray-700 leading-relaxed"><?php echo e($delivery->notes); ?></p>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.supplier', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\00. RENATA\CODE\indahberkahabadi\resources\views/supplier/deliveries/show.blade.php ENDPATH**/ ?>