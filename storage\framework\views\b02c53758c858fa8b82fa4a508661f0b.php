

<?php $__env->startSection('title', 'Dashboard Gudang - Indah Berkah Abadi'); ?>
<?php $__env->startSection('page-title', 'Dashboard Gudang'); ?>

<?php $__env->startSection('content'); ?>
<div class="admin-dashboard-layout-with-analytics">
    <!-- Analytics Sidebar Toggle Button -->
    <button class="admin-dashboard-analytics-toggle-btn" onclick="toggleAnalyticsSidebar()">
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
        </svg>
        <span>Analisis</span>
    </button>

    <!-- Main Dashboard Content -->
    <div class="admin-dashboard-main-content space-y-6">
    <!-- Welcome Header -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-content">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <h1 class="text-2xl sm:text-3xl font-bold text-gray-900">Selamat Datang, <?php echo e(auth()->user()->name); ?></h1>
                    <p class="text-gray-600 mt-1">Pantau dan kelola operasi inventori Indah Berkah Abadi</p>
                </div>
                <div class="flex flex-col sm:flex-row gap-3">
                    
                    
                    <a href="<?php echo e(route('admin.distributions.create')); ?>" class="admin-dashboard-btn admin-dashboard-btn-secondary">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                        </svg>
                        Buat Distribusi
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Stats Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6">
        <div class="admin-dashboard-stat-card">
            <div class="admin-dashboard-stat-icon blue">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                </svg>
            </div>
            <div class="admin-dashboard-stat-value"><?php echo e(number_format($stats['total_stock'] ?? 0)); ?></div>
            <div class="admin-dashboard-stat-label">Total Stok Gudang</div>
        </div>

        <div class="admin-dashboard-stat-card">
            <div class="admin-dashboard-stat-icon green">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                </svg>
            </div>
            <div class="admin-dashboard-stat-value"><?php echo e($stats['active_stores'] ?? 0); ?></div>
            <div class="admin-dashboard-stat-label">Toko Aktif</div>
        </div>

        <div class="admin-dashboard-stat-card">
            <div class="admin-dashboard-stat-icon purple">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                </svg>
            </div>
            <div class="admin-dashboard-stat-value"><?php echo e($stats['pending_distributions'] ?? 0); ?></div>
            <div class="admin-dashboard-stat-label">Distribusi Pending</div>
        </div>

        <div class="admin-dashboard-stat-card">
            <div class="admin-dashboard-stat-icon orange">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div class="admin-dashboard-stat-value"><?php echo e($stats['low_stock_items'] ?? 0); ?></div>
            <div class="admin-dashboard-stat-label">Stok Menipis</div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-header">
            <h2 class="admin-dashboard-card-title">Aksi Cepat</h2>
        </div>
        <div class="admin-dashboard-card-content">
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                <?php $__currentLoopData = $quickActions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $action): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <a href="<?php echo e(route($action['route'])); ?>" class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                    <div class="admin-dashboard-stat-icon <?php echo e($action['color']); ?> mr-4" style="width: 40px; height: 40px; margin-bottom: 0;">
                        <?php if($action['icon'] === 'box'): ?>
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                        </svg>
                        <?php elseif($action['icon'] === 'send'): ?>
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                        </svg>
                        <?php elseif($action['icon'] === 'store'): ?>
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                        <?php endif; ?>
                    </div>
                    <div>
                        <h3 class="font-semibold text-gray-900"><?php echo e($action['title']); ?></h3>
                        <p class="text-sm text-gray-600"><?php echo e($action['description']); ?></p>
                    </div>
                </a>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    </div>
    </div>

    <!-- Analytics Sidebar -->
    <?php echo $__env->make('admin.components.analytics-sidebar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
</div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\00. RENATA\CODE\indahberkahabadi\resources\views/admin/dashboard.blade.php ENDPATH**/ ?>