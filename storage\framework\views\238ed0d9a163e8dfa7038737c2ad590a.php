<?php $__env->startSection('title', 'Stock Produk - Indah Berkah Abadi'); ?>
<?php $__env->startSection('page-title', 'Stock Produk'); ?>

<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- Header -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-content">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <h1 class="text-2xl sm:text-3xl font-bold text-gray-900">Stock Produk</h1>
                    <p class="text-gray-600 mt-1">Pantau dan kelola stok produk di gudang pusat</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
        <div class="admin-dashboard-stat-card">
            <div class="admin-dashboard-stat-content">
                <div class="admin-dashboard-stat-icon blue">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                    </svg>
                </div>
                <div class="admin-dashboard-stat-info">
                    <p class="admin-dashboard-stat-label">Total Produk</p>
                    <p class="admin-dashboard-stat-value"><?php echo e(number_format($totalProducts)); ?></p>
                </div>
            </div>
        </div>

        <div class="admin-dashboard-stat-card">
            <div class="admin-dashboard-stat-content">
                <div class="admin-dashboard-stat-icon yellow">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                    </svg>
                </div>
                <div class="admin-dashboard-stat-info">
                    <p class="admin-dashboard-stat-label">Stok Gudang</p>
                    <p class="admin-dashboard-stat-value"><?php echo e(number_format($totalWarehouseStock)); ?></p>
                </div>
            </div>
        </div>

        <div class="admin-dashboard-stat-card">
            <div class="admin-dashboard-stat-content">
                <div class="admin-dashboard-stat-icon orange">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="admin-dashboard-stat-info">
                    <p class="admin-dashboard-stat-label">Pending Distribusi</p>
                    <p class="admin-dashboard-stat-value"><?php echo e(number_format($totalPendingDistributions)); ?></p>
                </div>
            </div>
        </div>

        <div class="admin-dashboard-stat-card">
            <div class="admin-dashboard-stat-content">
                <div class="admin-dashboard-stat-icon green">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="admin-dashboard-stat-info">
                    <p class="admin-dashboard-stat-label">Stok Tersedia</p>
                    <p class="admin-dashboard-stat-value"><?php echo e(number_format($totalAvailableStock)); ?></p>
                </div>
            </div>
        </div>

        <div class="admin-dashboard-stat-card">
            <div class="admin-dashboard-stat-content">
                <div class="admin-dashboard-stat-icon purple">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                    </svg>
                </div>
                <div class="admin-dashboard-stat-info">
                    <p class="admin-dashboard-stat-label">Stok Toko</p>
                    <p class="admin-dashboard-stat-value"><?php echo e(number_format($totalStoreStock)); ?></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filter -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-content">
            <form method="GET" action="<?php echo e(route('admin.stock.index')); ?>">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Cari Produk</label>
                        <input type="text" name="search" value="<?php echo e(request('search')); ?>" placeholder="Nama produk..." class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div class="flex items-end">
                        <button type="submit" class="admin-dashboard-btn admin-dashboard-btn-primary w-full">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                            Filter
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Products Table -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-header">
            <h2 class="admin-dashboard-card-title">Daftar Produk</h2>
        </div>
        <div class="admin-dashboard-card-content">
            <div class="overflow-x-auto">
                <table class="w-full text-sm text-left">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                        <tr>
                            <th class="px-6 py-3">Produk</th>
                            <th class="px-6 py-3">Stok Gudang</th>
                            <th class="px-6 py-3">Pending Distribusi</th>
                            <th class="px-6 py-3">Stok Tersedia</th>
                            <th class="px-6 py-3">Stok Toko</th>
                            <th class="px-6 py-3">Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr class="bg-white border-b hover:bg-gray-50">
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900"><?php echo e($product->name); ?></div>
                                <div class="text-sm text-gray-500">ID: <?php echo e(Str::limit($product->id, 8)); ?></div>
                            </td>
                            <td class="px-6 py-4">
                                <span class="font-medium text-gray-900"><?php echo e(number_format($product->getCurrentWarehouseStock())); ?></span>
                                <span class="text-sm text-gray-500">unit</span>
                            </td>
                            <td class="px-6 py-4">
                                <?php if($product->getPendingDistributionsQuantity() > 0): ?>
                                    <span class="font-medium text-orange-600"><?php echo e(number_format($product->getPendingDistributionsQuantity())); ?></span>
                                    <span class="text-sm text-orange-500">unit</span>
                                <?php else: ?>
                                    <span class="font-medium text-gray-400">0</span>
                                    <span class="text-sm text-gray-400">unit</span>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4">
                                <?php $availableStock = $product->getAvailableStock(); ?>
                                <span class="font-medium <?php echo e($availableStock > 0 ? 'text-green-600' : 'text-red-600'); ?>">
                                    <?php echo e(number_format($availableStock)); ?>

                                </span>
                                <span class="text-sm <?php echo e($availableStock > 0 ? 'text-green-500' : 'text-red-500'); ?>">unit</span>
                                <?php if($availableStock < 0): ?>
                                    <div class="text-xs text-red-500 mt-1">Stok tidak mencukupi!</div>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4">
                                <span class="font-medium text-blue-600"><?php echo e(number_format($product->storeStock->sum('quantity'))); ?></span>
                                <span class="text-sm text-blue-500">unit</span>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex items-center gap-2">
                                    <a href="<?php echo e(route('admin.stock.movements', $product)); ?>"
                                       class="text-blue-600 hover:text-blue-800 text-sm font-medium">Riwayat</a>
                                    <a href="<?php echo e(route('admin.stock.adjust-form', $product)); ?>"
                                       class="text-green-600 hover:text-green-800 text-sm font-medium">Sesuaikan</a>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                                Tidak ada produk yang ditemukan
                            </td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if($products->hasPages()): ?>
            <div class="mt-4">
                <?php echo e($products->links('pagination.admin-dashboard')); ?>

            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
.admin-dashboard-stat-icon.yellow {
    background: #fef3c7;
    color: #d97706;
}

.admin-dashboard-stat-icon.orange {
    background: #fed7aa;
    color: #ea580c;
}
</style>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\00. RENATA\CODE\indahberkahabadi\resources\views/admin/stock/index.blade.php ENDPATH**/ ?>