<?php $__env->startSection('title', 'Kelola Produk - Indah Berkah Abadi'); ?>
<?php $__env->startSection('page-title', 'Kelola Produk'); ?>

<?php $__env->startPush('styles'); ?>
<link rel="stylesheet" href="<?php echo e(asset('css/admin-dashboard-product-management.css')); ?>">
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script src="<?php echo e(asset('js/admin-dashboard-product-management.js')); ?>"></script>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- Header -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-content">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <h1 class="text-2xl sm:text-3xl font-bold text-gray-900">Kelola Produk</h1>
                    <p class="text-gray-600 mt-1">Manajemen produk dan inventori sistem</p>
                </div>
                <div class="flex flex-col sm:flex-row gap-3">
                    
                    
                    <a href="<?php echo e(route('admin.products.export', request()->query())); ?>" class="admin-dashboard-btn admin-dashboard-btn-secondary">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                        </svg>
                        Export Data
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Product Stats - Compact Version -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-content">
            <div class="grid grid-cols-2 lg:grid-cols-5 gap-4">
                <div class="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                        </svg>
                    </div>
                    <div>
                        <div class="text-lg font-bold text-gray-900"><?php echo e($stats['total'] ?? 0); ?></div>
                        <div class="text-xs text-gray-600">Total Produk</div>
                    </div>
                </div>

                <div class="flex items-center gap-3 p-3 bg-yellow-50 rounded-lg">
                    <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <svg class="w-4 h-4 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                        </svg>
                    </div>
                    <div>
                        <div class="text-lg font-bold text-gray-900"><?php echo e(number_format($stats['warehouse_stock'] ?? 0)); ?></div>
                        <div class="text-xs text-gray-600">Stok Gudang</div>
                    </div>
                </div>

                <div class="flex items-center gap-3 p-3 bg-orange-50 rounded-lg">
                    <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <svg class="w-4 h-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div>
                        <div class="text-lg font-bold text-gray-900"><?php echo e(number_format($stats['pending_distributions'] ?? 0)); ?></div>
                        <div class="text-xs text-gray-600">Pending Distribusi</div>
                    </div>
                </div>

                <div class="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div>
                        <div class="text-lg font-bold text-gray-900"><?php echo e(number_format($stats['available_stock'] ?? 0)); ?></div>
                        <div class="text-xs text-gray-600">Stok Tersedia</div>
                    </div>
                </div>

                <div class="flex items-center gap-3 p-3 bg-purple-50 rounded-lg">
                    <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                    </div>
                    <div>
                        <div class="text-lg font-bold text-gray-900"><?php echo e(number_format($stats['store_stock'] ?? 0)); ?></div>
                        <div class="text-xs text-gray-600">Stok Toko</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filter -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-content">
            <form method="GET" action="<?php echo e(route('admin.products.index')); ?>">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Cari Produk</label>
                        <input type="text" name="search" value="<?php echo e(request('search')); ?>" placeholder="Nama produk..." class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div class="flex items-end">
                        <button type="submit" class="admin-dashboard-btn admin-dashboard-btn-primary w-full">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                            Cari
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if(session('success')): ?>
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
            <span class="block sm:inline"><?php echo e(session('success')); ?></span>
        </div>
    <?php endif; ?>

    <?php if(session('error')): ?>
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
            <span class="block sm:inline"><?php echo e(session('error')); ?></span>
        </div>
    <?php endif; ?>

    <!-- Products Table -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-header">
            <h2 class="admin-dashboard-card-title">Daftar Produk</h2>
        </div>
        <div class="admin-dashboard-card-content">
            <div class="overflow-x-auto">
                <table class="w-full text-sm text-left">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                        <tr>
                            <th class="px-6 py-3">Nama Produk</th>
                            <th class="px-6 py-3">Stok Gudang</th>
                            <th class="px-6 py-3">Pending</th>
                            <th class="px-6 py-3">Tersedia</th>
                            <th class="px-6 py-3">Stok Toko</th>
                            <th class="px-6 py-3">Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr class="bg-white border-b hover:bg-gray-50">
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900"><?php echo e($product->name); ?></div>
                                <div class="text-xs text-gray-500"><?php echo e($product->created_at->format('d/m/Y')); ?></div>
                            </td>
                            <td class="px-6 py-4">
                                <span class="font-medium text-gray-900"><?php echo e(number_format($product->getCurrentWarehouseStock())); ?></span>
                                <span class="text-sm text-gray-500">unit</span>
                            </td>
                            <td class="px-6 py-4">
                                <?php if($product->getPendingDistributionsQuantity() > 0): ?>
                                    <span class="font-medium text-orange-600"><?php echo e(number_format($product->getPendingDistributionsQuantity())); ?></span>
                                    <span class="text-sm text-orange-500">unit</span>
                                <?php else: ?>
                                    <span class="font-medium text-gray-400">0</span>
                                    <span class="text-sm text-gray-400">unit</span>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4">
                                <?php $availableStock = $product->getAvailableStock(); ?>
                                <span class="font-medium <?php echo e($availableStock >= 0 ? 'text-green-600' : 'text-red-600'); ?>">
                                    <?php echo e(number_format($availableStock)); ?>

                                </span>
                                <span class="text-sm <?php echo e($availableStock >= 0 ? 'text-green-500' : 'text-red-500'); ?>">unit</span>
                                <?php if($availableStock < 0): ?>
                                    <div class="text-xs text-red-500 mt-1">Tidak mencukupi!</div>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4">
                                <span class="font-medium text-blue-600"><?php echo e(number_format($product->storeStock->sum('quantity'))); ?></span>
                                <span class="text-sm text-blue-500">unit</span>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex items-center gap-2">
                                    <a href="<?php echo e(route('admin.products.show', $product)); ?>" class="text-blue-600 hover:text-blue-800 text-sm font-medium">Lihat</a>
                                    <a href="<?php echo e(route('admin.products.edit', $product)); ?>" class="text-green-600 hover:text-green-800 text-sm font-medium">Edit</a>
                                    <button type="button"
                                            class="text-red-600 hover:text-red-800 text-sm font-medium"
                                            onclick="AdminProductManager.showDeleteConfirmation('<?php echo e($product->id); ?>', '<?php echo e(addslashes($product->name)); ?>', <?php echo e($product->getCurrentWarehouseStock()); ?>, <?php echo e($product->distributions()->count()); ?>, <?php echo e($product->stockOpname()->count()); ?>)">
                                        Hapus
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                                Tidak ada produk yang ditemukan
                            </td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if($products->hasPages()): ?>
            <div class="mt-4">
                <?php echo e($products->links('pagination.admin-dashboard')); ?>

            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Product Deletion Modal -->
<?php echo $__env->make('admin.components.product-deletion-modal', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

<style>
.admin-dashboard-stat-icon.red {
    background: #fee2e2;
    color: #dc2626;
}

.admin-dashboard-stat-icon.gray {
    background: #f3f4f6;
    color: #6b7280;
}
</style>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\00. RENATA\CODE\indahberkahabadi\resources\views/admin/products/index.blade.php ENDPATH**/ ?>